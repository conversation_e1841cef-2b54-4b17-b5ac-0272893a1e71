/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   gdt.h                                              :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <<EMAIL>>         +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/07/09 12:30:00 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/09 14:31:01 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#ifndef GDT_H
# define GDT_H

#include "../core/libs.h"
#include "../core/structs.h"

/* ──────────── GDT Selector Definitions ──────────── */
#define GDT_NULL_SEGMENT    0x00
#define GDT_KERNEL_CODE     0x08
#define GDT_KERNEL_DATA     0x10
#define GDT_KERNEL_STACK    0x18
#define GDT_USER_CODE       0x20
#define GDT_USER_DATA       0x28
#define GDT_USER_STACK      0x30

/* ──────────── GDT Function Prototypes ──────────── */

/**
 * @brief Set up a single GDT entry
 * @param num Entry index in the GDT
 * @param base Base address of the segment
 * @param limit Segment limit
 * @param access Access byte (permissions, ring level, etc.)
 * @param gran Granularity byte (size flags, etc.)
 */
void gdt_set_gate(int num, uint32_t base, uint32_t limit, uint8_t access, uint8_t gran);

/**
 * @brief Initialize and install the Global Descriptor Table
 * Sets up all required GDT entries and loads the GDT into the CPU
 */
void gdt_install(void);

/**
 * @brief Print detailed information about the current GDT
 * Displays all GDT entries with their properties for debugging
 */
void print_gdt_info(void);

/**
 * @brief Assembly function to flush the GDT
 * @param gdt_ptr Pointer to the GDT descriptor structure
 * This function is implemented in assembly and loads the new GDT
 */
extern void gdt_flush(t_gdt_ptr *gdt_ptr);

#endif