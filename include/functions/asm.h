/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   asm.h                                              :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <<EMAIL>>         +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/07/09 14:45:00 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/09 14:56:43 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#ifndef ASM_H
# define ASM_H

#include "../core/libs.h"
#include "../core/structs.h"

/* ──────────── Assembly Functions from src/asm/ ──────────── */

/* ──────────── GDT Assembly Functions (gdt_asm.s) ──────────── */

/**
 * @brief Flush the GDT and update segment registers
 * @param gdt_ptr Pointer to GDT descriptor structure
 * This function loads the new GDT and updates all segment registers
 */
extern void gdt_flush(t_gdt_ptr *gdt_ptr);

/* ──────────── IDT Assembly Functions (idt.s) ──────────── */

/**
 * @brief Load IDT descriptor into CPU
 * @param idt_descriptor Pointer to IDT descriptor structure
 */
extern void IDT_Load(t_idt_descryptor *idt_descriptor);

/**
 * @brief Kernel panic function - halts the system
 * Disables interrupts and halts the CPU
 */
extern void kernelPanic(void);

/* ──────────── Utility Assembly Functions (utils.s) ──────────── */

/**
 * @brief Enable interrupts globally
 * Executes STI instruction
 */
extern void EnableInterrupts(void);

/**
 * @brief Disable interrupts globally
 * Executes CLI instruction
 */
extern void DisableInterrupts(void);

/**
 * @brief Low-level port output (byte)
 * @param port Port number to write to
 * @param value Byte value to write
 */
extern void i686_outb(uint16_t port, uint8_t value);

/**
 * @brief Low-level port input (byte)
 * @param port Port number to read from
 * @return Byte value read from port
 */
extern uint8_t i686_inb(uint16_t port);

/**
 * @brief Crash function for testing
 * Triggers interrupt 50 and division by zero
 */
extern void crash_me(void);

/* ──────────── ISR Assembly Functions (isr.s) ──────────── */
/* All ISR functions are generated by macros in isr.s */

/**
 * @brief Common ISR handler (called by all ISR stubs)
 * @param regs Pointer to register state
 * This is the C function called by the assembly ISR stubs
 */
extern void ISR_Handler(t_registers *regs);

/* ISR0-ISR255 function declarations */
extern void ISR0(void);   extern void ISR1(void);   extern void ISR2(void);   extern void ISR3(void);
extern void ISR4(void);   extern void ISR5(void);   extern void ISR6(void);   extern void ISR7(void);
extern void ISR8(void);   extern void ISR9(void);   extern void ISR10(void);  extern void ISR11(void);
extern void ISR12(void);  extern void ISR13(void);  extern void ISR14(void);  extern void ISR15(void);
extern void ISR16(void);  extern void ISR17(void);  extern void ISR18(void);  extern void ISR19(void);
extern void ISR20(void);  extern void ISR21(void);  extern void ISR22(void);  extern void ISR23(void);
extern void ISR24(void);  extern void ISR25(void);  extern void ISR26(void);  extern void ISR27(void);
extern void ISR28(void);  extern void ISR29(void);  extern void ISR30(void);  extern void ISR31(void);
extern void ISR32(void);  extern void ISR33(void);  extern void ISR34(void);  extern void ISR35(void);
extern void ISR36(void);  extern void ISR37(void);  extern void ISR38(void);  extern void ISR39(void);
extern void ISR40(void);  extern void ISR41(void);  extern void ISR42(void);  extern void ISR43(void);
extern void ISR44(void);  extern void ISR45(void);  extern void ISR46(void);  extern void ISR47(void);
extern void ISR48(void);  extern void ISR49(void);  extern void ISR50(void);  extern void ISR51(void);
extern void ISR52(void);  extern void ISR53(void);  extern void ISR54(void);  extern void ISR55(void);
extern void ISR56(void);  extern void ISR57(void);  extern void ISR58(void);  extern void ISR59(void);
extern void ISR60(void);  extern void ISR61(void);  extern void ISR62(void);  extern void ISR63(void);
extern void ISR64(void);  extern void ISR65(void);  extern void ISR66(void);  extern void ISR67(void);
extern void ISR68(void);  extern void ISR69(void);  extern void ISR70(void);  extern void ISR71(void);
extern void ISR72(void);  extern void ISR73(void);  extern void ISR74(void);  extern void ISR75(void);
extern void ISR76(void);  extern void ISR77(void);  extern void ISR78(void);  extern void ISR79(void);
extern void ISR80(void);  extern void ISR81(void);  extern void ISR82(void);  extern void ISR83(void);
extern void ISR84(void);  extern void ISR85(void);  extern void ISR86(void);  extern void ISR87(void);
extern void ISR88(void);  extern void ISR89(void);  extern void ISR90(void);  extern void ISR91(void);
extern void ISR92(void);  extern void ISR93(void);  extern void ISR94(void);  extern void ISR95(void);
extern void ISR96(void);  extern void ISR97(void);  extern void ISR98(void);  extern void ISR99(void);
extern void ISR100(void); extern void ISR101(void); extern void ISR102(void); extern void ISR103(void);
extern void ISR104(void); extern void ISR105(void); extern void ISR106(void); extern void ISR107(void);
extern void ISR108(void); extern void ISR109(void); extern void ISR110(void); extern void ISR111(void);
extern void ISR112(void); extern void ISR113(void); extern void ISR114(void); extern void ISR115(void);
extern void ISR116(void); extern void ISR117(void); extern void ISR118(void); extern void ISR119(void);
extern void ISR120(void); extern void ISR121(void); extern void ISR122(void); extern void ISR123(void);
extern void ISR124(void); extern void ISR125(void); extern void ISR126(void); extern void ISR127(void);
extern void ISR128(void); extern void ISR129(void); extern void ISR130(void); extern void ISR131(void);
extern void ISR132(void); extern void ISR133(void); extern void ISR134(void); extern void ISR135(void);
extern void ISR136(void); extern void ISR137(void); extern void ISR138(void); extern void ISR139(void);
extern void ISR140(void); extern void ISR141(void); extern void ISR142(void); extern void ISR143(void);
extern void ISR144(void); extern void ISR145(void); extern void ISR146(void); extern void ISR147(void);
extern void ISR148(void); extern void ISR149(void); extern void ISR150(void); extern void ISR151(void);
extern void ISR152(void); extern void ISR153(void); extern void ISR154(void); extern void ISR155(void);
extern void ISR156(void); extern void ISR157(void); extern void ISR158(void); extern void ISR159(void);
extern void ISR160(void); extern void ISR161(void); extern void ISR162(void); extern void ISR163(void);
extern void ISR164(void); extern void ISR165(void); extern void ISR166(void); extern void ISR167(void);
extern void ISR168(void); extern void ISR169(void); extern void ISR170(void); extern void ISR171(void);
extern void ISR172(void); extern void ISR173(void); extern void ISR174(void); extern void ISR175(void);
extern void ISR176(void); extern void ISR177(void); extern void ISR178(void); extern void ISR179(void);
extern void ISR180(void); extern void ISR181(void); extern void ISR182(void); extern void ISR183(void);
extern void ISR184(void); extern void ISR185(void); extern void ISR186(void); extern void ISR187(void);
extern void ISR188(void); extern void ISR189(void); extern void ISR190(void); extern void ISR191(void);
extern void ISR192(void); extern void ISR193(void); extern void ISR194(void); extern void ISR195(void);
extern void ISR196(void); extern void ISR197(void); extern void ISR198(void); extern void ISR199(void);
extern void ISR200(void); extern void ISR201(void); extern void ISR202(void); extern void ISR203(void);
extern void ISR204(void); extern void ISR205(void); extern void ISR206(void); extern void ISR207(void);
extern void ISR208(void); extern void ISR209(void); extern void ISR210(void); extern void ISR211(void);
extern void ISR212(void); extern void ISR213(void); extern void ISR214(void); extern void ISR215(void);
extern void ISR216(void); extern void ISR217(void); extern void ISR218(void); extern void ISR219(void);
extern void ISR220(void); extern void ISR221(void); extern void ISR222(void); extern void ISR223(void);
extern void ISR224(void); extern void ISR225(void); extern void ISR226(void); extern void ISR227(void);
extern void ISR228(void); extern void ISR229(void); extern void ISR230(void); extern void ISR231(void);
extern void ISR232(void); extern void ISR233(void); extern void ISR234(void); extern void ISR235(void);
extern void ISR236(void); extern void ISR237(void); extern void ISR238(void); extern void ISR239(void);
extern void ISR240(void); extern void ISR241(void); extern void ISR242(void); extern void ISR243(void);
extern void ISR244(void); extern void ISR245(void); extern void ISR246(void); extern void ISR247(void);
extern void ISR248(void); extern void ISR249(void); extern void ISR250(void); extern void ISR251(void);
extern void ISR252(void); extern void ISR253(void); extern void ISR254(void); extern void ISR255(void);

#endif
