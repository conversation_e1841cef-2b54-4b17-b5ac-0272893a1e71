/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   kernel.h                                           :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <<EMAIL>>         +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/07/09 12:30:00 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/09 21:32:23 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#ifndef KERNEL_FUNCTIONS_H
# define KERNEL_FUNCTIONS_H

#include "../core/libs.h"
#include "../core/structs.h"

/* ──────────── Terminal/VGA Functions ──────────── */

void terminal_initialize(void);
void terminal_setcolor(uint8_t color);
void terminal_putentryat(char c, uint8_t color, size_t x, size_t y);
void terminal_putchar(char c);
void terminal_writestring(const char *data);
void terminal_offset(uint16_t offset);
void terminal_restore(void);

/* ──────────── Utility Functions ──────────── */

void printnbr(int nbr, int base);
size_t ft_strlen(const char *str);
int ft_strcmp(const char *s1, const char *s2);
void *ft_memcpy(void *dest, const void *src, size_t n);
void *ft_memset(void *s, int c, size_t n);

/* ──────────── Keyboard Functions ──────────── */

void keyboard_init(void);
void keyboard_handler(t_registers *regs);
void update_cursor(int scancode);

/* ──────────── Stack Functions ──────────── */

void stack_push(uint32_t value);
uint32_t stack_pop(void);
uint32_t stack_peek(void);
int stack_is_empty(void);
int stack_size(void);
void print_kernel_stack(void);

/* ──────────── VGA/Hardware Functions ──────────── */

uint8_t vga_entry_color(enum vga_color fg, enum vga_color bg);
uint16_t vga_entry(unsigned char uc, uint8_t color);
void vga_set_cursor(size_t row, size_t col);
void vga_cursor_restore(void);

/* ──────────── Additional Utility Functions ──────────── */

void *ft_memmove(void *dest, const void *src, size_t n);
void *ft_memchr(const void *s, int c, size_t n);
void ft_bzero(void *s, size_t n);
int ft_memcmp(const void *s1, const void *s2, size_t n);
int strcmp(const char *s1, const char *s2);
void shell_initialize(void);

/* ──────────── I/O Functions ──────────── */

uint8_t inb(uint16_t port);
void outb(uint16_t port, uint8_t value);
void outw(uint16_t port, uint16_t value);

/* ──────────── Exception Handling Functions ──────────── */

const char *get_exception_message(uint32_t exception_num);

#endif