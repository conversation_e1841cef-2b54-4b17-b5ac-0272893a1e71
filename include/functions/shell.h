/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   shell.h                                            :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <<EMAIL>>         +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/07/09 12:30:00 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/09 14:32:32 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#ifndef SHELL_H
# define SHELL_H

#include "../core/libs.h"
#include "../core/structs.h"

/* ──────────── Shell Function Prototypes ──────────── */

/**
 * @brief Handle input character from keyboard
 * @param c Character received from keyboard
 */
void shell_handle_input(char c);

/**
 * @brief Process a complete command string
 * @param command Command string to execute
 */
void shell_process_command(const char *command);

/* ──────────── Shell Command Functions ──────────── */

/**
 * @brief Display help information
 */
void cmd_help(void);

/**
 * @brief Clear the screen
 */
void cmd_clear(void);

/**
 * @brief Display system information
 */
void cmd_sysinfo(void);

/**
 * @brief Display memory information
 */
void cmd_meminfo(void);

/**
 * @brief Reboot the system
 */
void cmd_reboot(void);

/**
 * @brief Halt the system
 */
void cmd_halt(void);

/**
 * @brief Display GDT information
 */
void cmd_gdt(void);

/**
 * @brief Display IDT information
 */
void cmd_idt(void);

/**
 * @brief Test stack operations
 */
void cmd_stack_test(void);

/**
 * @brief Display stack contents
 */
void cmd_stack_show(void);

/**
 * @brief Push value onto stack
 * @param arg String representation of value to push
 */
void cmd_stack_push(const char *arg);

/**
 * @brief Pop value from stack
 */
void cmd_stack_pop(void);

/**
 * @brief Peek at top of stack
 */
void cmd_stack_peek(void);

/**
 * @brief Echo command arguments
 * @param args Arguments to echo
 */
void cmd_echo(const char *args);

/**
 * @brief Display current date/time
 */
void cmd_date(void);

/**
 * @brief Display system uptime
 */
void cmd_uptime(void);

/**
 * @brief Display process information
 */
void cmd_ps(void);

/**
 * @brief Display kernel version
 */
void cmd_version(void);

/**
 * @brief Test command for development
 */
void cmd_test(void);

/* ──────────── Utility Functions ──────────── */

/**
 * @brief Parse hexadecimal string to integer
 * @param arg String containing hex number
 * @return Parsed integer value
 */
uint32_t parse_hex(const char *arg);

/**
 * @brief Print hexadecimal value
 * @param value Value to print in hex
 */
void print_hex(uint32_t value);

/**
 * @brief Print hexadecimal byte
 * @param value Byte value to print in hex
 */
void print_hex_byte(uint8_t value);

#endif