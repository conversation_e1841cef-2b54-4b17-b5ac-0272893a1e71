/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   shell.h                                            :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <<EMAIL>>         +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/07/09 12:30:00 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/09 21:18:31 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#ifndef SHELL_H
# define SHELL_H

#include "../core/enums.h"
#include "../core/libs.h"
#include "../core/structs.h"

/* ──────────── Shell Function Prototypes ──────────── */

/**
 * @brief Handle input character from keyboard
 * @param c Character received from keyboard
 */
void shell_handle_input(char c);

/**
 * @brief Process a complete command string
 * @param command Command string to execute
 */
void shell_process_command(const char *command);

/* ──────────── Shell Command Handler Functions ──────────── */
/* These are the actual functions implemented in shell.c */

/**
 * @brief Display help information
 */
void handle_help(void);

/**
 * @brief Clear the screen
 */
void handle_clear(void);

/**
 * @brief Display system information
 */
void handle_sysinfo(void);

/**
 * @brief Display memory information
 */
void handle_memory(void);

/**
 * @brief Reboot the system
 */
void handle_reboot(void);

/**
 * @brief Halt the system
 */
void handle_halt(void);

/**
 * @brief Shutdown the system
 */
void handle_shutdown(void);

/**
 * @brief Display stack contents
 */
void handle_stack(void);

/**
 * @brief Display GDT information
 */
void handle_gdt(void);

/**
 * @brief Display segment information
 */
void handle_segments(void);

/**
 * @brief Test GDT operations
 */
void handle_gdttest(void);

/**
 * @brief Test stack operations
 */
void handle_stacktest(void);

/**
 * @brief Display interrupt status
 */
void handle_interrupts(void);

/**
 * @brief Push value onto stack
 * @param arg String representation of value to push
 */
void handle_push(const char *arg);

/**
 * @brief Pop value from stack
 */
void handle_pop(void);

/**
 * @brief Handle unknown command
 * @param command Unknown command string
 */
void handle_unknown(const char *command);

/* ──────────── KFS2 Advanced Command Handlers ──────────── */

/**
 * @brief Main KFS2 command handler
 * @param args KFS2 command arguments
 */
void handle_kfs2(const char *args);

/**
 * @brief Display KFS2 help information
 */
void display_kfs2_help(void);

/* ──────────── Component-Specific Help Functions ──────────── */

/**
 * @brief Display core system help
 */
void display_core_system_help(void);

/**
 * @brief Display GDT & memory help
 */
void display_gdt_memory_help(void);

/**
 * @brief Display stack operations help
 */
void display_stack_operations_help(void);

/**
 * @brief Display system info help
 */
void display_system_info_help(void);

/**
 * @brief Handle KFS2 core subsystem
 * @param subcommand Core subcommand
 */
void handle_kfs2_core(const char *subcommand);

/**
 * @brief Handle KFS2 GDT subsystem
 * @param subcommand GDT subcommand
 */
void handle_kfs2_gdt(const char *subcommand);

/**
 * @brief Handle KFS2 memory subsystem
 * @param subcommand Memory subcommand
 */
void handle_kfs2_memory(const char *subcommand);

/**
 * @brief Handle KFS2 stack subsystem
 * @param subcommand Stack subcommand
 */
void handle_kfs2_stack(const char *subcommand);

/**
 * @brief Handle KFS2 sysinfo subsystem
 * @param subcommand Sysinfo subcommand
 */
void handle_kfs2_sysinfo(const char *subcommand);

/* ──────────── Utility Functions ──────────── */

/**
 * @brief Get command type from command string
 * @param command Command string
 * @return Command type enumeration
 */
command_type_t get_command_type(const char *command);

/* ──────────── Modular Command System ──────────── */

/**
 * @brief Get general command type
 * @param command Command string
 * @return Command type or CMD_UNKNOWN
 */
command_type_t get_general_command_type(const char *command);

/**
 * @brief Get KFS2 command type
 * @param command Command string
 * @return Command type or CMD_UNKNOWN
 */
command_type_t get_kfs2_command_type(const char *command);

/* ──────────── Component-Specific Command Type Getters ──────────── */

/**
 * @brief Get core system command type
 * @param command Command string
 * @return Command type or CMD_UNKNOWN
 */
command_type_t get_core_system_command_type(const char *command);

/**
 * @brief Get GDT & memory command type
 * @param command Command string
 * @return Command type or CMD_UNKNOWN
 */
command_type_t get_gdt_memory_command_type(const char *command);

/**
 * @brief Get stack operations command type
 * @param command Command string
 * @return Command type or CMD_UNKNOWN
 */
command_type_t get_stack_operations_command_type(const char *command);

/**
 * @brief Get system info command type
 * @param command Command string
 * @return Command type or CMD_UNKNOWN
 */
command_type_t get_system_info_command_type(const char *command);

/**
 * @brief Get KFS2 advanced command type
 * @param command Command string
 * @return Command type or CMD_UNKNOWN
 */
command_type_t get_kfs2_advanced_command_type(const char *command);

/**
 * @brief Get KFS3 memory command type
 * @param command Command string
 * @return Command type or CMD_UNKNOWN
 */
command_type_t get_kfs3_memory_command_type(const char *command);

/**
 * @brief Get KFS3 crash command type
 * @param command Command string
 * @return Command type or CMD_UNKNOWN
 */
command_type_t get_kfs3_crash_command_type(const char *command);

/**
 * @brief Handle general commands
 * @param cmd_type Command type
 * @param arg Command argument
 * @return true if command was handled, false otherwise
 */
bool handle_general_commands(command_type_t cmd_type, const char *arg);

/**
 * @brief Handle KFS2 commands
 * @param cmd_type Command type
 * @param arg Command argument
 * @return true if command was handled, false otherwise
 */
bool handle_kfs2_commands(command_type_t cmd_type, const char *arg);

/* ──────────── Component-Specific Command Handlers ──────────── */

/**
 * @brief Handle core system commands
 * @param cmd_type Command type
 * @param arg Command argument
 * @return true if command was handled, false otherwise
 */
bool handle_core_system_commands(command_type_t cmd_type, const char *arg);

/**
 * @brief Handle GDT & memory commands
 * @param cmd_type Command type
 * @param arg Command argument
 * @return true if command was handled, false otherwise
 */
bool handle_gdt_memory_commands(command_type_t cmd_type, const char *arg);

/**
 * @brief Handle stack operations commands
 * @param cmd_type Command type
 * @param arg Command argument
 * @return true if command was handled, false otherwise
 */
bool handle_stack_operations_commands(command_type_t cmd_type, const char *arg);

/**
 * @brief Handle system info commands
 * @param cmd_type Command type
 * @param arg Command argument
 * @return true if command was handled, false otherwise
 */
bool handle_system_info_commands(command_type_t cmd_type, const char *arg);

/**
 * @brief Handle KFS2 advanced commands
 * @param cmd_type Command type
 * @param arg Command argument
 * @return true if command was handled, false otherwise
 */
bool handle_kfs2_advanced_commands(command_type_t cmd_type, const char *arg);

/**
 * @brief Handle KFS3 memory commands
 * @param cmd_type Command type
 * @param arg Command argument
 * @return true if command was handled, false otherwise
 */
bool handle_kfs3_memory_commands(command_type_t cmd_type, const char *arg);

/**
 * @brief Handle KFS3 crash commands
 * @param cmd_type Command type
 * @param arg Command argument
 * @return true if command was handled, false otherwise
 */
bool handle_kfs3_crash_commands(command_type_t cmd_type, const char *arg);

#endif