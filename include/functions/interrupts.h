/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   interrupts.h                                       :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <<EMAIL>>         +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/07/09 12:30:00 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/09 14:35:48 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#ifndef INTERRUPTS_H
# define INTERRUPTS_H

#include "../core/libs.h"
#include "../core/structs.h"

/* ──────────── IDT Function Prototypes ──────────── */

/**
 * @brief Set up a single IDT entry
 * @param num Interrupt number (0-255)
 * @param base Address of the interrupt handler
 * @param selector Code segment selector
 * @param flags IDT entry flags (gate type, privilege level, etc.)
 */
void idt_set_gate(uint8_t num, uint32_t base, uint16_t selector, uint8_t flags);

/**
 * @brief Initialize and install the Interrupt Descriptor Table
 */
void idt_install(void);

/* ──────────── ISR Function Prototypes ──────────── */

/**
 * @brief Initialize all Interrupt Service Routines
 */
void isr_install(void);

/**
 * @brief Set up ISR gates in the IDT
 */
void ISR_InitializeGates(void);

/**
 * @brief Common ISR handler called by all ISR stubs
 * @param regs Pointer to register state at time of interrupt
 */
void isr_handler(t_registers *regs);

/* ──────────── IRQ Function Prototypes ──────────── */

/**
 * @brief Initialize all Interrupt Request handlers
 */
void irq_install(void);

/**
 * @brief Install a custom IRQ handler
 * @param irq IRQ number (0-15)
 * @param handler Function pointer to the handler
 */
void irq_install_handler(int irq, IRQHandler handler);

/**
 * @brief Uninstall an IRQ handler
 * @param irq IRQ number (0-15)
 */
void irq_uninstall_handler(int irq);

/**
 * @brief Common IRQ handler called by all IRQ stubs
 * @param regs Pointer to register state at time of interrupt
 */
void irq_handler(t_registers *regs);

/* ──────────── PIC Function Prototypes ──────────── */

/**
 * @brief Initialize and remap the Programmable Interrupt Controller
 */
void pic_remap(void);

/**
 * @brief Send End-Of-Interrupt signal to PIC
 * @param irq IRQ number that finished processing
 */
void pic_send_eoi(uint8_t irq);

/* ──────────── I/O Function Prototypes ──────────── */

/**
 * @brief Read a byte from an I/O port
 * @param port Port number to read from
 * @return Byte value read from the port
 */
uint8_t inb(uint16_t port);

/**
 * @brief Write a byte to an I/O port
 * @param port Port number to write to
 * @param value Byte value to write
 */
void outb(uint16_t port, uint8_t value);

/**
 * @brief Read a word from an I/O port
 * @param port Port number to read from
 * @return Word value read from the port
 */
uint16_t inw(uint16_t port);

/**
 * @brief Write a word to an I/O port
 * @param port Port number to write to
 * @param value Word value to write
 */
void outw(uint16_t port, uint16_t value);

/* ──────────── Assembly ISR/IRQ Stubs ──────────── */
extern void isr0(void);   extern void isr1(void);   extern void isr2(void);   extern void isr3(void);
extern void isr4(void);   extern void isr5(void);   extern void isr6(void);   extern void isr7(void);
extern void isr8(void);   extern void isr9(void);   extern void isr10(void);  extern void isr11(void);
extern void isr12(void);  extern void isr13(void);  extern void isr14(void);  extern void isr15(void);
extern void isr16(void);  extern void isr17(void);  extern void isr18(void);  extern void isr19(void);
extern void isr20(void);  extern void isr21(void);  extern void isr22(void);  extern void isr23(void);
extern void isr24(void);  extern void isr25(void);  extern void isr26(void);  extern void isr27(void);
extern void isr28(void);  extern void isr29(void);  extern void isr30(void);  extern void isr31(void);

extern void irq0(void);   extern void irq1(void);   extern void irq2(void);   extern void irq3(void);
extern void irq4(void);   extern void irq5(void);   extern void irq6(void);   extern void irq7(void);
extern void irq8(void);   extern void irq9(void);   extern void irq10(void);  extern void irq11(void);
extern void irq12(void);  extern void irq13(void);  extern void irq14(void);  extern void irq15(void);

/* ──────────── Additional IDT Functions ──────────── */

/**
 * @brief Load IDT descriptor into CPU
 * @param idtDescriptor Pointer to IDT descriptor
 */
void IDT_Load(t_idt_descryptor* idtDescriptor);

/**
 * @brief Enable interrupts globally
 */
void EnableInterrupts(void);

/**
 * @brief Disable interrupts globally
 */
void DisableInterrupts(void);

/**
 * @brief Set up an IDT gate
 * @param interrupt Interrupt number
 * @param base Handler function address
 * @param segmentDescriptor Code segment selector
 * @param flags IDT flags
 */
void IDT_SetGate(int interrupt, void *base, uint16_t segmentDescriptor, uint8_t flags);

/**
 * @brief Initialize the IDT
 */
void IDT_Initialize(void);

/**
 * @brief Enable a specific IDT gate
 * @param interrupt Interrupt number to enable
 */
void IDT_EnableGate(int interrupt);

/**
 * @brief Disable a specific IDT gate
 * @param interrupt Interrupt number to disable
 */
void IDT_DisableGate(int interrupt);

/**
 * @brief Initialize ISR system
 */
void ISR_Initialize(void);

/**
 * @brief Register an ISR handler
 * @param interrupt Interrupt number
 * @param handler Handler function
 */
void ISR_RegisterHandler(int interrupt, ISRHandler handler);

/**
 * @brief I/O wait function
 */
void iowait(void);

/**
 * @brief Configure PIC with offsets
 * @param offsetPic1 Master PIC offset
 * @param offsetPic2 Slave PIC offset
 */
void PIC_Configure(uint8_t offsetPic1, uint8_t offsetPic2);

/**
 * @brief Send End-Of-Interrupt to PIC
 * @param irq IRQ number
 */
void PIC_SendEOF(int irq);

/**
 * @brief Mask a specific IRQ
 * @param irq IRQ number to mask
 */
void PIC_Mask(int irq);

/**
 * @brief Unmask a specific IRQ
 * @param irq IRQ number to unmask
 */
void PIC_Unmask(int irq);

/**
 * @brief Disable PIC entirely
 */
void PIC_Disable(void);

/**
 * @brief Read IRQ request register
 * @return IRQ request register value
 */
uint16_t PIC_ReadIRQRequestReg(void);

/**
 * @brief Read in-service register
 * @return In-service register value
 */
uint16_t PIC_ReadInServiceReg(void);

/**
 * @brief Initialize IRQ system
 */
void IRQ_Initialize(void);

/**
 * @brief Register an IRQ handler
 * @param irq IRQ number
 * @param handler Handler function
 */
void IRQ_RegisterHandler(int irq, IRQHandler handler);

/**
 * @brief Low-level I/O functions
 */
void i686_outb(uint16_t port, uint8_t value);
uint8_t i686_inb(uint16_t port);

/* ──────────── Handler Functions ──────────── */

/**
 * @brief Timer interrupt handler
 * @param regs Register state
 */
void timer(t_registers* regs);

#endif