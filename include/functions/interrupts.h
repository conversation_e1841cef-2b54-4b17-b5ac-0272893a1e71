/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   interrupts.h                                       :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <<EMAIL>>         +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/07/09 12:30:00 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/09 16:11:53 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#ifndef INTERRUPTS_H
# define INTERRUPTS_H

#include "../core/libs.h"
#include "../core/structs.h"

/* ──────────── IDT Function Prototypes ──────────── */

/**
 * @brief Set up an IDT gate
 * @param interrupt Interrupt number
 * @param base Handler function address
 * @param segmentDescriptor Code segment selector
 * @param flags IDT flags
 */
void IDT_SetGate(int interrupt, void *base, uint16_t segmentDescriptor, uint8_t flags);

/**
 * @brief Initialize the IDT
 */
void IDT_Initialize(void);

/**
 * @brief Enable a specific IDT gate
 * @param interrupt Interrupt number to enable
 */
void IDT_EnableGate(int interrupt);

/**
 * @brief Disable a specific IDT gate
 * @param interrupt Interrupt number to disable
 */
void IDT_DisableGate(int interrupt);

/* ──────────── ISR Function Prototypes ──────────── */

/**
 * @brief Initialize ISR system
 */
void ISR_Initialize(void);

/**
 * @brief Set up ISR gates in the IDT
 */
void ISR_InitializeGates(void);

/**
 * @brief Register an ISR handler
 * @param interrupt Interrupt number
 * @param handler Handler function
 */
void ISR_RegisterHandler(int interrupt, ISRHandler handler);

/* ──────────── IRQ Function Prototypes ──────────── */

/**
 * @brief Initialize IRQ system
 */
void IRQ_Initialize(void);

/**
 * @brief Register an IRQ handler
 * @param irq IRQ number
 * @param handler Handler function
 */
void IRQ_RegisterHandler(int irq, IRQHandler handler);

/* ──────────── PIC Function Prototypes ──────────── */

/**
 * @brief Configure PIC with offsets
 * @param offsetPic1 Master PIC offset
 * @param offsetPic2 Slave PIC offset
 */
void PIC_Configure(uint8_t offsetPic1, uint8_t offsetPic2);

/**
 * @brief Send End-Of-Interrupt to PIC
 * @param irq IRQ number
 */
void PIC_SendEOF(int irq);

/**
 * @brief Mask a specific IRQ
 * @param irq IRQ number to mask
 */
void PIC_Mask(int irq);

/**
 * @brief Unmask a specific IRQ
 * @param irq IRQ number to unmask
 */
void PIC_Unmask(int irq);

/**
 * @brief Disable PIC entirely
 */
void PIC_Disable(void);

/**
 * @brief Read IRQ request register
 * @return IRQ request register value
 */
uint16_t PIC_ReadIRQRequestReg(void);

/**
 * @brief Read in-service register
 * @return In-service register value
 */
uint16_t PIC_ReadInServiceReg(void);

/* ──────────── I/O Function Prototypes ──────────── */

/**
 * @brief I/O wait function
 */
void iowait(void);

/* ──────────── Handler Functions ──────────── */

/**
 * @brief Timer interrupt handler
 * @param regs Register state
 */
void timer(t_registers* regs);

#endif