/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   globals.h                                          :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <<EMAIL>>         +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/07/09 12:30:00 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/09 14:36:34 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#ifndef GLOBALS_H
# define GLOBALS_H

#include "structs.h"

/* ──────────── Global Variables ──────────── */

/**
 * @brief Main kernel instance
 * This structure contains all kernel state including:
 * - Interrupt handlers (ISR and IRQ)
 * - IDT and descriptor
 * - Screen management
 * - Terminal state
 * - Shell command buffer
 * - Kernel stack
 */
extern t_kernel kernel;

#endif