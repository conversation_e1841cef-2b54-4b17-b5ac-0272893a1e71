/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   shell.c                                            :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <<EMAIL>>         +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/06/17 21:58:20 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/09 21:09:22 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#include "../include/kernel.h"

void shell_initialize() {
    terminal_writestring("KFS Shell v1.0\n");
    terminal_writestring("Type 'help' for available commands\n");
    terminal_writestring("> ");
}

void handle_help() {
    terminal_writestring("Available commands:\n");
    terminal_writestring("  help         - Display this help message\n");

    // Display component-specific help sections
    display_core_system_help();
    display_gdt_memory_help();
    display_stack_operations_help();
    display_system_info_help();

    // Display KFS2 advanced commands
    display_kfs2_help();
}

void handle_unknown(const char* command) {
    terminal_writestring("Unknown command: ");
    terminal_writestring(command);
    terminal_writestring("\n");
}

/* ──────────── KFS2 Command Type Getter ──────────── */
command_type_t get_kfs2_command_type(const char* command) {
    if (ft_strcmp(command, "stack") == 0)
        return CMD_STACK;
    else if (ft_strcmp(command, "gdt") == 0)
        return CMD_GDT;
    else if (ft_strcmp(command, "segments") == 0)
        return CMD_SEGMENTS;
    else if (ft_strcmp(command, "memory") == 0)
        return CMD_MEMORY;
    else if (ft_strcmp(command, "gdttest") == 0)
        return CMD_GDTTEST;
    else if (ft_strcmp(command, "stacktest") == 0)
        return CMD_STACKTEST;
    else if (ft_strcmp(command, "sysinfo") == 0)
        return CMD_SYSINFO;
    else if (ft_strcmp(command, "interrupts") == 0)
        return CMD_INTERRUPTS;
    else if (ft_strcmp(command, "push") == 0)
        return CMD_PUSH;
    else if (ft_strcmp(command, "pop") == 0)
        return CMD_POP;
    else if (ft_strcmp(command, "clear") == 0)
        return CMD_CLEAR;
    else if (ft_strcmp(command, "reboot") == 0)
        return CMD_REBOOT;
    else if (ft_strcmp(command, "halt") == 0)
        return CMD_HALT;
    else if (ft_strcmp(command, "shutdown") == 0)
        return CMD_SHUTDOWN;
    else if (ft_strcmp(command, "kfs2") == 0)
        return CMD_KFS2;

    return CMD_UNKNOWN;
}

/* ──────────── General Command Type Getter ──────────── */
command_type_t get_general_command_type(const char* command) {
    if (ft_strcmp(command, "help") == 0)
        return CMD_HELP;

    return CMD_UNKNOWN;
}

/* ──────────── General Command Handler ──────────── */
bool handle_general_commands(command_type_t cmd_type, const char* arg) {
    (void)arg;
    switch (cmd_type) {
        case CMD_HELP:
            handle_help();
            return true;
        default:
            return false;
    }
}
/* ──────────── Main Command Type Getter ──────────── */
command_type_t get_command_type(const char* command) {
    command_type_t cmd_type;

    cmd_type = get_general_command_type(command);
    if (cmd_type != CMD_UNKNOWN)
        return cmd_type;

    cmd_type = get_kfs2_command_type(command);
    if (cmd_type != CMD_UNKNOWN)
        return cmd_type;

    cmd_type = get_kfs3_memory_command_type(command);
    if (cmd_type != CMD_UNKNOWN)
        return cmd_type;

    cmd_type = get_kfs3_crash_command_type(command);
    if (cmd_type != CMD_UNKNOWN)
        return cmd_type;

    return CMD_UNKNOWN;
}

/* ──────────── KFS2 Command Handler ──────────── */
bool handle_kfs2_commands(command_type_t cmd_type, const char* arg) {
    switch (cmd_type) {
        case CMD_STACK:
            handle_stack();
            return true;
        case CMD_GDT:
            handle_gdt();
            return true;
        case CMD_SEGMENTS:
            handle_segments();
            return true;
        case CMD_MEMORY:
            handle_memory();
            return true;
        case CMD_GDTTEST:
            handle_gdttest();
            return true;
        case CMD_STACKTEST:
            handle_stacktest();
            return true;
        case CMD_SYSINFO:
            handle_sysinfo();
            return true;
        case CMD_INTERRUPTS:
            handle_interrupts();
            return true;
        case CMD_PUSH:
            handle_push(arg);
            return true;
        case CMD_POP:
            handle_pop();
            return true;
        case CMD_CLEAR:
            handle_clear();
            return true;
        case CMD_REBOOT:
            handle_reboot();
            return true;
        case CMD_HALT:
            handle_halt();
            return true;
        case CMD_SHUTDOWN:
            handle_shutdown();
            return true;
        case CMD_KFS2:
            handle_kfs2(arg);
            return true;
        default:
            return false;
    }
}

void shell_process_command(const char* cmd) {
    char command[32] = {0};
    char arg[32] = {0};
    int i = 0, j = 0;

    while (cmd[i] && cmd[i] != ' ' && i < 31) {
        command[i] = cmd[i];
        i++;
    }
    command[i] = '\0';

    while (cmd[i] && cmd[i] == ' ')
        i++;

    while (cmd[i] && j < 31) {
        arg[j] = cmd[i];
        i++;
        j++;
    }
    arg[j] = '\0';

    command_type_t cmd_type = get_command_type(command);
    if (handle_general_commands(cmd_type, arg)) {
        /* General command was handled */
    } else if (handle_kfs2_commands(cmd_type, arg)) {
        /* KFS2 command was handled */
    } else if (cmd_type == CMD_UNKNOWN) {
        if (command[0] != '\0')
            handle_unknown(command);
    }

    terminal_writestring("> ");
}

void shell_handle_input(char c) {
    if (c == '\n') {
        terminal_putchar('\n');
        kernel.command_buffer[kernel.buffer_pos] = '\0';
        shell_process_command(kernel.command_buffer);
        kernel.buffer_pos = 0;
    } else if (c == '\b' && 0 < kernel.buffer_pos) {
        kernel.buffer_pos--;
        terminal_putchar('\b');
        terminal_putchar(' ');
        terminal_putchar('\b');
    } else if (' ' <= c && c <= '~' && kernel.buffer_pos < COMMAND_BUFFER_SIZE - 1) {
        kernel.command_buffer[kernel.buffer_pos++] = c;
        terminal_putchar(c);
    }
}
