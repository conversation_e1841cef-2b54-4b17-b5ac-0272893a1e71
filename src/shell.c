/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   shell.c                                            :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <<EMAIL>>         +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/06/17 21:58:20 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/09 20:53:58 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#include "../include/kernel.h"

void shell_initialize() {
    terminal_writestring("KFS Shell v1.0\n");
    terminal_writestring("Type 'help' for available commands\n");
    terminal_writestring("> ");
}

void handle_help() {
    terminal_writestring("Available commands:\n");
    terminal_writestring("  help         - Display this help message\n");
    terminal_writestring("=== Core System ===\n");
    terminal_writestring("  clear        - Clear the screen\n");
    terminal_writestring("  reboot       - Reboot the system\n");
    terminal_writestring("  halt         - Halt the system\n");
    terminal_writestring("  shutdown     - Shutdown the system\n");
    terminal_writestring("\n=== GDT & Memory ===\n");
    terminal_writestring("  gdt          - Display GDT information\n");
    terminal_writestring("  segments     - Show current segment registers\n");
    terminal_writestring("  memory       - Show memory layout and addresses\n");
    terminal_writestring("  gdttest      - Test GDT segment switching\n");
    terminal_writestring("\n=== Stack Operations ===\n");
    terminal_writestring("  stack        - Print the kernel stack\n");
    terminal_writestring("  push <hex>   - Push a value onto the stack\n");
    terminal_writestring("  pop          - Pop a value from the stack\n");
    terminal_writestring("  stacktest    - Demonstrate stack operations\n");
    terminal_writestring("\n=== System Info ===\n");
    terminal_writestring("  sysinfo      - Show complete system information\n");
    terminal_writestring("  interrupts   - Show interrupt status\n");

    // Display KFS2 advanced commands
    display_kfs2_help();
}

void handle_unknown(const char* command) {
    terminal_writestring("Unknown command: ");
    terminal_writestring(command);
    terminal_writestring("\n");
}

command_type_t get_command_type(const char* command) {
	command_type_t cmd_type;

	cmd_type = CMD_UNKNOWN;
    if (ft_strcmp(command, "help") == 0)
		cmd_type = CMD_HELP;
    else if (ft_strcmp(command, "stack") == 0)
		cmd_type = CMD_STACK;
    else if (ft_strcmp(command, "gdt") == 0)
		cmd_type = CMD_GDT;
    else if (ft_strcmp(command, "segments") == 0)
		cmd_type = CMD_SEGMENTS;
    else if (ft_strcmp(command, "memory") == 0)
		cmd_type = CMD_MEMORY;
    else if (ft_strcmp(command, "gdttest") == 0)
		cmd_type = CMD_GDTTEST;
    else if (ft_strcmp(command, "stacktest") == 0)
		cmd_type = CMD_STACKTEST;
    else if (ft_strcmp(command, "sysinfo") == 0)
		cmd_type = CMD_SYSINFO;
    else if (ft_strcmp(command, "interrupts") == 0)
		cmd_type = CMD_INTERRUPTS;
    else if (ft_strcmp(command, "push") == 0)
		cmd_type = CMD_PUSH;
    else if (ft_strcmp(command, "pop") == 0)
		cmd_type = CMD_POP;
    else if (ft_strcmp(command, "clear") == 0)
		cmd_type = CMD_CLEAR;
    else if (ft_strcmp(command, "reboot") == 0)
		cmd_type = CMD_REBOOT;
    else if (ft_strcmp(command, "halt") == 0)
		cmd_type = CMD_HALT;
    else if (ft_strcmp(command, "shutdown") == 0)
		cmd_type = CMD_SHUTDOWN;
    else if (ft_strcmp(command, "kfs2") == 0)
		cmd_type = CMD_KFS2;
	return cmd_type;
}

void shell_process_command(const char* cmd) {
    char command[32] = {0};
    char arg[32] = {0};
    int i = 0, j = 0;

    while (cmd[i] && cmd[i] != ' ' && i < 31) {
        command[i] = cmd[i];
        i++;
    }
    command[i] = '\0';

    while (cmd[i] && cmd[i] == ' ')
        i++;

    while (cmd[i] && j < 31) {
        arg[j] = cmd[i];
        i++;
        j++;
    }
    arg[j] = '\0';

    command_type_t cmd_type = get_command_type(command);

    switch (cmd_type) {
        case CMD_HELP:
            handle_help();
            break;
        case CMD_STACK:
            handle_stack();
            break;
        case CMD_GDT:
            handle_gdt();
            break;
        case CMD_SEGMENTS:
            handle_segments();
            break;
        case CMD_MEMORY:
            handle_memory();
            break;
        case CMD_GDTTEST:
            handle_gdttest();
            break;
        case CMD_STACKTEST:
            handle_stacktest();
            break;
        case CMD_SYSINFO:
            handle_sysinfo();
            break;
        case CMD_INTERRUPTS:
            handle_interrupts();
            break;
        case CMD_PUSH:
            handle_push(arg);
            break;
        case CMD_POP:
            handle_pop();
            break;
        case CMD_CLEAR:
            handle_clear();
            break;
        case CMD_REBOOT:
            handle_reboot();
            break;
        case CMD_HALT:
            handle_halt();
            break;
        case CMD_SHUTDOWN:
            handle_shutdown();
            break;
        case CMD_KFS2:
            handle_kfs2(arg);
            break;
        case CMD_UNKNOWN:
            if (command[0] != '\0')
                handle_unknown(command);
            break;
    }

    terminal_writestring("> ");
}

void shell_handle_input(char c) {
    if (c == '\n') {
        terminal_putchar('\n');
        kernel.command_buffer[kernel.buffer_pos] = '\0';
        shell_process_command(kernel.command_buffer);
        kernel.buffer_pos = 0;
    } else if (c == '\b' && 0 < kernel.buffer_pos) {
        kernel.buffer_pos--;
        terminal_putchar('\b');
        terminal_putchar(' ');
        terminal_putchar('\b');
    } else if (' ' <= c && c <= '~' && kernel.buffer_pos < COMMAND_BUFFER_SIZE - 1) {
        kernel.command_buffer[kernel.buffer_pos++] = c;
        terminal_putchar(c);
    }
}
