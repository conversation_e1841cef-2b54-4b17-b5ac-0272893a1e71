/* ************************************************************************** */
/*                                                                            */
/*                                                        :::      ::::::::   */
/*   kfs2_handle.c                                      :+:      :+:    :+:   */
/*                                                    +:+ +:+         +:+     */
/*   By: rperez-t <<EMAIL>>         +#+  +:+       +#+        */
/*                                                +#+#+#+#+#+   +#+           */
/*   Created: 2025/07/09 20:14:06 by rperez-t          #+#    #+#             */
/*   Updated: 2025/07/09 20:51:23 by rperez-t         ###   ########.fr       */
/*                                                                            */
/* ************************************************************************** */

#include "../../../include/kernel.h"

/* ──────────── KFS2 Core Handlers ──────────── */

void handle_kfs2_core(const char *subcommand) {
    if (!subcommand || subcommand[0] == '\0') {
        terminal_writestring("=== KFS2 Core System ===\n");
        terminal_writestring("Available core commands:\n");
        terminal_writestring("  kfs2 core interrupts  - Show interrupt system status\n");
        terminal_writestring("  kfs2 core idt         - Show IDT information\n");
        terminal_writestring("  kfs2 core pic         - Show PIC configuration\n");
        terminal_writestring("  kfs2 core cpu         - Show CPU information\n");
        return;
    }

    if (strcmp(subcommand, "interrupts") == 0) {
        handle_interrupts();
    } else if (strcmp(subcommand, "idt") == 0) {
        handle_idt();
    } else if (strcmp(subcommand, "pic") == 0) {
        handle_pic();
    } else if (strcmp(subcommand, "cpu") == 0) {
        handle_cpu();
    } else {
        terminal_writestring("Unknown core subcommand: ");
        terminal_writestring(subcommand);
        terminal_writestring("\nUse 'kfs2 core' for available commands.\n");
    }
}

void handle_kfs2_gdt(const char *subcommand) {
    if (!subcommand || subcommand[0] == '\0') {
        terminal_writestring("=== KFS2 GDT System ===\n");
        terminal_writestring("Available GDT commands:\n");
        terminal_writestring("  kfs2 gdt info         - Show GDT information\n");
        terminal_writestring("  kfs2 gdt segments     - Show segment details\n");
        terminal_writestring("  kfs2 gdt test         - Test GDT operations\n");
        terminal_writestring("  kfs2 gdt dump         - Dump GDT entries\n");
        return;
    }

    if (strcmp(subcommand, "info") == 0) {
        handle_gdt();
    } else if (strcmp(subcommand, "segments") == 0) {
        handle_segments();
    } else if (strcmp(subcommand, "test") == 0) {
        handle_gdttest();
    } else if (strcmp(subcommand, "dump") == 0) {
        handle_gdt_dump();
    } else {
        terminal_writestring("Unknown GDT subcommand: ");
        terminal_writestring(subcommand);
        terminal_writestring("\nUse 'kfs2 gdt' for available commands.\n");
    }
}

void handle_kfs2_memory(const char *subcommand) {
    if (!subcommand || subcommand[0] == '\0') {
        terminal_writestring("=== KFS2 Memory System ===\n");
        terminal_writestring("Available memory commands:\n");
        terminal_writestring("  kfs2 memory info      - Show memory layout\n");
        terminal_writestring("  kfs2 memory usage     - Show memory usage\n");
        terminal_writestring("  kfs2 memory map       - Show memory map\n");
        terminal_writestring("  kfs2 memory test      - Test memory operations\n");
        return;
    }

    if (strcmp(subcommand, "info") == 0) {
        handle_memory();
    } else if (strcmp(subcommand, "usage") == 0) {
        handle_memory_usage();
    } else if (strcmp(subcommand, "map") == 0) {
        handle_memory_map();
    } else if (strcmp(subcommand, "test") == 0) {
        handle_memory_test();
    } else {
        terminal_writestring("Unknown memory subcommand: ");
        terminal_writestring(subcommand);
        terminal_writestring("\nUse 'kfs2 memory' for available commands.\n");
    }
}

void handle_kfs2_stack(const char *subcommand) {
    if (!subcommand || subcommand[0] == '\0') {
        terminal_writestring("=== KFS2 Stack System ===\n");
        terminal_writestring("Available stack commands:\n");
        terminal_writestring("  kfs2 stack info       - Show stack information\n");
        terminal_writestring("  kfs2 stack show       - Display stack contents\n");
        terminal_writestring("  kfs2 stack test       - Test stack operations\n");
        terminal_writestring("  kfs2 stack push <val> - Push value to stack\n");
        terminal_writestring("  kfs2 stack pop        - Pop value from stack\n");
        return;
    }

    if (strcmp(subcommand, "info") == 0) {
        handle_stack_info();
    } else if (strcmp(subcommand, "show") == 0) {
        handle_stack();
    } else if (strcmp(subcommand, "test") == 0) {
        handle_stacktest();
    } else if (strncmp(subcommand, "push ", 5) == 0) {
        handle_push(subcommand + 5);
    } else if (strcmp(subcommand, "pop") == 0) {
        handle_pop();
    } else {
        terminal_writestring("Unknown stack subcommand: ");
        terminal_writestring(subcommand);
        terminal_writestring("\nUse 'kfs2 stack' for available commands.\n");
    }
}

void handle_kfs2_sysinfo(const char *subcommand) {
    if (!subcommand || subcommand[0] == '\0') {
        terminal_writestring("=== KFS2 System Information ===\n");
        terminal_writestring("Available sysinfo commands:\n");
        terminal_writestring("  kfs2 sysinfo full     - Complete system information\n");
        terminal_writestring("  kfs2 sysinfo kernel   - Kernel information\n");
        terminal_writestring("  kfs2 sysinfo hardware - Hardware information\n");
        terminal_writestring("  kfs2 sysinfo version  - Version information\n");
        return;
    }

    if (strcmp(subcommand, "full") == 0) {
        handle_sysinfo();
    } else if (strcmp(subcommand, "kernel") == 0) {
        handle_kernel_info();
    } else if (strcmp(subcommand, "hardware") == 0) {
        handle_hardware_info();
    } else if (strcmp(subcommand, "version") == 0) {
        handle_version_info();
    } else {
        terminal_writestring("Unknown sysinfo subcommand: ");
        terminal_writestring(subcommand);
        terminal_writestring("\nUse 'kfs2 sysinfo' for available commands.\n");
    }
}

/* ──────────── Main KFS2 Handler ──────────── */

void handle_kfs2(const char *args) {
    if (!args || args[0] == '\0') {
        terminal_writestring("=== KFS2 Advanced System Interface ===\n");
        terminal_writestring("Available subsystems:\n");
        terminal_writestring("  kfs2 core      - Core system operations\n");
        terminal_writestring("  kfs2 gdt       - Global Descriptor Table\n");
        terminal_writestring("  kfs2 memory    - Memory management\n");
        terminal_writestring("  kfs2 stack     - Stack operations\n");
        terminal_writestring("  kfs2 sysinfo   - System information\n");
        terminal_writestring("\nUse 'kfs2 <subsystem>' for detailed commands.\n");
        return;
    }

    char subsystem[32] = {0};
    char subcommand[64] = {0};
    int i = 0, j = 0;

    // Extract subsystem
    while (args[i] && args[i] != ' ' && i < 31) {
        subsystem[i] = args[i];
        i++;
    }
    subsystem[i] = '\0';

    // Skip spaces
    while (args[i] && args[i] == ' ')
        i++;

    // Extract subcommand (rest of the string)
    while (args[i] && j < 63) {
        subcommand[j] = args[i];
        i++;
        j++;
    }
    subcommand[j] = '\0';

    // Route to appropriate handler
    if (strcmp(subsystem, "core") == 0) {
        handle_kfs2_core(subcommand);
    } else if (strcmp(subsystem, "gdt") == 0) {
        handle_kfs2_gdt(subcommand);
    } else if (strcmp(subsystem, "memory") == 0) {
        handle_kfs2_memory(subcommand);
    } else if (strcmp(subsystem, "stack") == 0) {
        handle_kfs2_stack(subcommand);
    } else if (strcmp(subsystem, "sysinfo") == 0) {
        handle_kfs2_sysinfo(subcommand);
    } else {
        terminal_writestring("Unknown KFS2 subsystem: ");
        terminal_writestring(subsystem);
        terminal_writestring("\nUse 'kfs2' for available subsystems.\n");
    }
}

/* ──────────── KFS2 Help Display ──────────── */

void display_kfs2_help(void) {
    terminal_writestring("\n=== KFS2 Advanced Commands ===\n");
    terminal_writestring("  kfs2 core      - Core system (interrupts, IDT, PIC, CPU)\n");
    terminal_writestring("  kfs2 gdt       - GDT operations (info, segments, test)\n");
    terminal_writestring("  kfs2 memory    - Memory management (info, usage, map)\n");
    terminal_writestring("  kfs2 stack     - Stack operations (info, show, push, pop)\n");
    terminal_writestring("  kfs2 sysinfo   - System information (full, kernel, hardware)\n");
}
